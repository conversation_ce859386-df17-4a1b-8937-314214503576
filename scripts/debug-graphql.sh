#!/bin/bash

# Debug GraphQL Schema Loading
# This script helps debug GraphQL schema loading issues in production

set -e

echo "=== GraphQL Debug Information ==="
echo "Current working directory: $(pwd)"
echo "Date: $(date)"
echo ""

# Check if we're in the right directory
if [ ! -f "config.yaml" ]; then
    echo "❌ Not in the correct directory. Looking for config.yaml..."
    find / -name "config.yaml" -type f 2>/dev/null | head -5
    echo ""
fi

# Check GraphQL generated files
echo "=== Generated Files Check ==="
GENERATED_FILES=(
    "internal/controller/graphql/generated.go"
    "internal/controller/admin/graphql/generated.go"
)

for file in "${GENERATED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
        echo "   Size: $(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "unknown") bytes"
        echo "   Modified: $(stat -c%y "$file" 2>/dev/null || stat -f%Sm "$file" 2>/dev/null || echo "unknown")"
        
        # Check for embed directive
        if grep -q "go:embed" "$file" 2>/dev/null; then
            echo "   ✅ Contains go:embed directive"
            grep "go:embed" "$file" | head -3
        else
            echo "   ❌ No go:embed directive found"
        fi
    else
        echo "❌ $file missing"
    fi
    echo ""
done

# Check schema files
echo "=== Schema Files Check ==="
SCHEMA_FILES=(
    "internal/controller/graphql/schema.graphqls"
    "internal/controller/admin/graphql/schema.graphqls"
    "internal/controller/admin/graphql/schemas/admin_activity_cashback.gql"
)

for file in "${SCHEMA_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
        echo "   Size: $(stat -c%s "$file" 2>/dev/null || stat -f%z "$file" 2>/dev/null || echo "unknown") bytes"
        echo "   First few lines:"
        head -3 "$file" | sed 's/^/     /'
    else
        echo "❌ $file missing"
    fi
    echo ""
done

# Check Go environment
echo "=== Go Environment ==="
if command -v go >/dev/null 2>&1; then
    echo "Go version: $(go version)"
    echo "GOPATH: ${GOPATH:-not set}"
    echo "GOROOT: ${GOROOT:-not set}"
else
    echo "❌ Go not found in PATH"
fi

echo ""
echo "=== Environment Variables ==="
echo "APP_ENV: ${APP_ENV:-not set}"
echo "SERVER_PORT: ${SERVER_PORT:-not set}"
echo "DATABASE_URL: ${DATABASE_URL:+[REDACTED]}"

echo ""
echo "=== Process Information ==="
echo "Current user: $(whoami)"
echo "Process ID: $$"
echo "Available memory: $(free -h 2>/dev/null | grep Mem || echo "unknown")"

echo ""
echo "=== Debug Complete ==="
