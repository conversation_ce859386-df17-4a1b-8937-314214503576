#!/bin/bash

# Check GraphQL Generation Script
# This script verifies that GraphQL files are properly generated

set -e

echo "=== GraphQL Generation Check ==="

# Check if required generated files exist
REQUIRED_FILES=(
    "internal/controller/graphql/generated.go"
    "internal/controller/admin/graphql/generated.go"
    "internal/controller/graphql/gql_model/models_gen.go"
    "internal/controller/admin/graphql/gql_model/models_gen.go"
)

MISSING_FILES=()

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
        echo "❌ Missing: $file"
    else
        echo "✅ Found: $file"
    fi
done

# Check schema files
SCHEMA_FILES=(
    "internal/controller/graphql/schema.graphqls"
    "internal/controller/admin/graphql/schema.graphqls"
    "internal/controller/admin/graphql/schemas/admin_activity_cashback.gql"
)

for file in "${SCHEMA_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Missing schema: $file"
        MISSING_FILES+=("$file")
    else
        echo "✅ Found schema: $file"
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo ""
    echo "❌ GraphQL generation incomplete. Missing files:"
    for file in "${MISSING_FILES[@]}"; do
        echo "   - $file"
    done
    echo ""
    echo "Run 'make gqlgen' to generate missing files."
    exit 1
else
    echo ""
    echo "✅ All GraphQL files are properly generated!"
    exit 0
fi
