#!/bin/bash

# Complete Deployment Test Script
# This script tests the entire deployment pipeline to ensure GraphQL works

set -e

echo "=== XBIT Agent Deployment Test ==="
echo "Testing complete deployment pipeline..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "ℹ️  $message"
            ;;
    esac
}

# Test 1: Check GraphQL generation
echo "=== Test 1: GraphQL Generation ==="
if make gqlgen > /dev/null 2>&1; then
    print_status "SUCCESS" "GraphQL generation completed"
else
    print_status "ERROR" "GraphQL generation failed"
    exit 1
fi

if ./scripts/check-graphql.sh > /dev/null 2>&1; then
    print_status "SUCCESS" "All GraphQL files generated correctly"
else
    print_status "ERROR" "GraphQL files missing or incomplete"
    exit 1
fi
echo ""

# Test 2: Local build
echo "=== Test 2: Local Build ==="
if make build-local > /dev/null 2>&1; then
    print_status "SUCCESS" "Local build completed"
else
    print_status "ERROR" "Local build failed"
    exit 1
fi
echo ""

# Test 3: Docker build
echo "=== Test 3: Docker Build ==="
if make docker-build > /dev/null 2>&1; then
    print_status "SUCCESS" "Docker build completed"
else
    print_status "ERROR" "Docker build failed"
    exit 1
fi
echo ""

# Test 4: Docker GraphQL verification
echo "=== Test 4: Docker GraphQL Verification ==="
if docker run --rm xbit-agent:latest ./scripts/debug-graphql.sh > /dev/null 2>&1; then
    print_status "SUCCESS" "GraphQL files verified in Docker image"
else
    print_status "WARNING" "GraphQL verification in Docker had issues (check manually)"
fi
echo ""

# Test 5: Start local server and test endpoints
echo "=== Test 5: Endpoint Testing ==="
print_status "INFO" "Starting local server for endpoint testing..."

# Start server in background
make run-local > /tmp/server.log 2>&1 &
SERVER_PID=$!

# Wait for server to start
sleep 10

# Test endpoints
if ./scripts/test-graphql-endpoint.sh > /dev/null 2>&1; then
    print_status "SUCCESS" "GraphQL endpoints working correctly"
    ENDPOINT_TEST_PASSED=1
else
    print_status "ERROR" "GraphQL endpoints failed"
    ENDPOINT_TEST_PASSED=0
fi

# Stop server
kill $SERVER_PID 2>/dev/null || true
wait $SERVER_PID 2>/dev/null || true

if [ "$ENDPOINT_TEST_PASSED" -eq 0 ]; then
    echo ""
    print_status "INFO" "Server logs:"
    tail -20 /tmp/server.log
    exit 1
fi
echo ""

# Test 6: Clean up
echo "=== Test 6: Cleanup ==="
docker rmi xbit-agent:latest > /dev/null 2>&1 || true
rm -f /tmp/server.log
print_status "SUCCESS" "Cleanup completed"
echo ""

# Final summary
echo "=== Deployment Test Summary ==="
print_status "SUCCESS" "All tests passed! Deployment pipeline is working correctly."
echo ""
echo "Next steps:"
echo "1. Deploy to staging/production environment"
echo "2. Run: ./scripts/test-graphql-endpoint.sh <your-domain> <port>"
echo "3. Monitor logs for any GraphQL schema loading issues"
echo ""
print_status "INFO" "If you encounter issues in production, run the debug script:"
echo "   docker exec <container-id> ./scripts/debug-graphql.sh"
