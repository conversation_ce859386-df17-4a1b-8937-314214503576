#!/bin/bash

# Test GraphQL Endpoint Script
# This script tests if GraphQL endpoints are working properly

set -e

# Configuration
HOST=${1:-"127.0.0.1"}
PORT=${2:-"8080"}
BASE_URL="http://${HOST}:${PORT}"

USER_GRAPHQL_URL="${BASE_URL}/api/dex-agent/graphql"
ADMIN_GRAPHQL_URL="${BASE_URL}/api/dex-agent/admin/graphql"

echo "=== GraphQL Endpoint Test ==="
echo "Testing endpoints on ${BASE_URL}"
echo ""

# Function to test endpoint
test_endpoint() {
    local url=$1
    local name=$2
    local query=$3
    
    echo "Testing ${name} endpoint: ${url}"
    
    # Test if endpoint is reachable
    if curl -s --connect-timeout 5 --max-time 10 "${url}" > /dev/null; then
        echo "✅ ${name} endpoint is reachable"
    else
        echo "❌ ${name} endpoint is not reachable"
        return 1
    fi
    
    # Test GraphQL introspection query
    local introspection_query='{"query":"query IntrospectionQuery { __schema { queryType { name } } }"}'
    
    echo "Testing GraphQL schema loading..."
    local response=$(curl -s --connect-timeout 5 --max-time 10 \
        -X POST \
        -H "Content-Type: application/json" \
        -d "${introspection_query}" \
        "${url}" 2>/dev/null || echo "ERROR")
    
    if [[ "$response" == "ERROR" ]]; then
        echo "❌ ${name} GraphQL request failed"
        return 1
    elif echo "$response" | grep -q "Could not load GraphQL schema"; then
        echo "❌ ${name} GraphQL schema loading failed"
        echo "Response: $response"
        return 1
    elif echo "$response" | grep -q "__schema"; then
        echo "✅ ${name} GraphQL schema loaded successfully"
        return 0
    else
        echo "⚠️  ${name} GraphQL response unclear"
        echo "Response: $response"
        return 1
    fi
}

# Test User GraphQL endpoint
echo "=== Testing User GraphQL ==="
if test_endpoint "$USER_GRAPHQL_URL" "User GraphQL"; then
    echo "✅ User GraphQL endpoint working"
else
    echo "❌ User GraphQL endpoint failed"
    USER_FAILED=1
fi
echo ""

# Test Admin GraphQL endpoint
echo "=== Testing Admin GraphQL ==="
if test_endpoint "$ADMIN_GRAPHQL_URL" "Admin GraphQL"; then
    echo "✅ Admin GraphQL endpoint working"
else
    echo "❌ Admin GraphQL endpoint failed"
    ADMIN_FAILED=1
fi
echo ""

# Test playground endpoints
echo "=== Testing Playground Endpoints ==="
USER_PLAYGROUND_URL="${USER_GRAPHQL_URL}/playground"
ADMIN_PLAYGROUND_URL="${ADMIN_GRAPHQL_URL}/playground"

for playground_url in "$USER_PLAYGROUND_URL" "$ADMIN_PLAYGROUND_URL"; do
    if curl -s --connect-timeout 5 --max-time 10 "$playground_url" | grep -q "GraphQL"; then
        echo "✅ Playground reachable: $playground_url"
    else
        echo "❌ Playground not reachable: $playground_url"
    fi
done

echo ""
echo "=== Test Summary ==="
if [[ -z "$USER_FAILED" && -z "$ADMIN_FAILED" ]]; then
    echo "✅ All GraphQL endpoints are working properly!"
    exit 0
else
    echo "❌ Some GraphQL endpoints failed:"
    [[ -n "$USER_FAILED" ]] && echo "  - User GraphQL endpoint failed"
    [[ -n "$ADMIN_FAILED" ]] && echo "  - Admin GraphQL endpoint failed"
    exit 1
fi
