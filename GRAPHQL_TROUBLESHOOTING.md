# GraphQL Schema Loading Troubleshooting Guide

## Problem Description
When deploying to production/staging environments, you may encounter the error:
```
Could not load GraphQL schema.
```

This error occurs when GraphQL schema files are not properly embedded in the Docker image.

## Root Cause Analysis

### Why it works locally (`make run-local`):
1. GraphQL code is generated by `make gqlgen-check` before building
2. Schema files (`.graphqls`, `.gql`) are available in the source directory
3. Go embed can read these files during compilation

### Why it fails in production:
1. Docker build may not generate GraphQL code properly
2. Schema files are missing from the Docker image
3. Go embed cannot find the required schema files
4. Application panics with "codegen problem: %s not available"

## Solution Implemented

### 1. Updated Dockerfile
- Added `make` to Alpine dependencies
- Added `RUN make gqlgen` before building
- Added GraphQL generation verification
- Added debug script copying

### 2. Created Debug Scripts
- `scripts/check-graphql.sh` - Verifies GraphQL generation
- `scripts/debug-graphql.sh` - Debug information for production
- `scripts/test-graphql-endpoint.sh` - Tests GraphQL endpoints

### 3. Enhanced Entrypoint
- Added startup debug information
- Added GraphQL file verification
- Better error reporting

## How to Test the Fix

### Local Testing
```bash
# Test GraphQL generation
make gqlgen-status
./scripts/check-graphql.sh

# Test local build
make build-local
make run-local

# Test endpoints
./scripts/test-graphql-endpoint.sh
```

### Docker Testing
```bash
# Build and test Docker image
make docker-build-test

# Run Docker container locally
make docker-run-local

# Test endpoints in Docker
./scripts/test-graphql-endpoint.sh localhost 8080
```

### Production Deployment
```bash
# Build production image
docker build -t xbit-agent:production .

# Check if GraphQL files are properly embedded
docker run --rm xbit-agent:production ./scripts/debug-graphql.sh

# Deploy and test
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query":"query { __schema { queryType { name } } }"}' \
  https://your-domain/api/dex-agent/admin/graphql
```

## Key Files Modified

1. **Dockerfile** - Added GraphQL generation step
2. **entrypoint.sh** - Added debug information
3. **Makefile** - Added Docker testing targets
4. **scripts/check-graphql.sh** - GraphQL verification
5. **scripts/debug-graphql.sh** - Production debugging
6. **scripts/test-graphql-endpoint.sh** - Endpoint testing

## Verification Checklist

Before deploying to production, ensure:

- [ ] `make gqlgen` runs successfully
- [ ] All generated files exist:
  - `internal/controller/graphql/generated.go`
  - `internal/controller/admin/graphql/generated.go`
  - `internal/controller/graphql/gql_model/models_gen.go`
  - `internal/controller/admin/graphql/gql_model/models_gen.go`
- [ ] Schema files exist:
  - `internal/controller/admin/graphql/schema.graphqls`
  - `internal/controller/admin/graphql/schemas/admin_activity_cashback.gql`
- [ ] Docker build completes without errors
- [ ] GraphQL endpoints respond correctly

## Emergency Debugging

If the issue persists in production:

1. **Check container logs:**
   ```bash
   docker logs <container-id>
   ```

2. **Run debug script in container:**
   ```bash
   docker exec <container-id> ./scripts/debug-graphql.sh
   ```

3. **Verify file structure:**
   ```bash
   docker exec <container-id> find . -name "*.graphqls" -o -name "*.gql"
   ```

4. **Check generated files:**
   ```bash
   docker exec <container-id> ls -la internal/controller/*/graphql/generated.go
   ```

## Contact Information

If you continue to experience issues, please provide:
1. Container logs
2. Output of debug script
3. Docker build logs
4. Environment details (staging/production)
