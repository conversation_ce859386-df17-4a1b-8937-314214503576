#!/bin/sh
set -e

_terminate() {
  echo "Signal (SIGINT/SIGTERM) received. Waiting for gracefully shutdown..."
  kill $(jobs -p)
  wait
  exit 0
}

trap _terminate SIGINT SIGTERM

echo "=== XBIT Agent Starting ==="
echo "Environment: ${APP_ENV:-unknown}"
echo "Working directory: $(pwd)"
echo "Available files:"
ls -la | head -10

# Debug GraphQL files if debug script exists
if [ -f "scripts/debug-graphql.sh" ]; then
    echo "Running GraphQL debug check..."
    chmod +x scripts/debug-graphql.sh
    ./scripts/debug-graphql.sh || echo "Debug script failed, continuing..."
fi

# Run migration
export DATABASE_URL="postgresql://$POSTGRES_AGENCY_USER:$POSTGRES_AGENCY_PASS@$POSTGRES_AGENCY_HOST:$POSTGRES_AGENCY_PORT/agent?sslmode=${POSTGRES_AGENCY_SSL_MODE:-disable}"
echo "Running database migrations..."
atlas migrate apply --url $DATABASE_URL --dir file://migrations


# Wait for any process to exit
# wait -n

# Exit with status of process that exited first
exec $@
